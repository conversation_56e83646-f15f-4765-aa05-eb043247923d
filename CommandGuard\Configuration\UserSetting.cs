﻿using Sunny.UI;

namespace CommandGuard.Configuration
{
    [ConfigFile("Configuration\\UserSetting.ini")]
    public class UserSetting : IniConfig<UserSetting>
    {
        [ConfigSection("CommandGuard")] public int ImgType { get; set; }
        [ConfigSection("CommandGuard")] public int 开盘时间 { get; set; }
        [ConfigSection("CommandGuard")] public int 封盘时间 { get; set; }

        public override void SetDefault()
        {
            base.SetDefault();
            ImgType = 1;
            开盘时间 = 260;
            封盘时间 = 30;
        }
    }
}