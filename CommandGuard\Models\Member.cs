﻿using FreeSql.DataAnnotations;

namespace CommandGuard.Models;

/// <summary>
/// 会员信息模型类 - 彩票机器人系统的用户管理核心模型
///
/// 功能说明：
/// - 存储系统中所有用户的基本信息和账户数据
/// - 支持用户身份识别、余额管理、层级关系
/// - 提供完整的用户权限和属性管理
/// - 支持拉手返利和回水机制
///
/// 核心功能：
/// 1. 用户身份管理：账号、昵称、备注名
/// 2. 财务管理：余额、回水比例
/// 3. 层级关系：拉手上级、返利比例
/// 4. 用户分类：真人用户、假人用户
///
/// 业务场景：
/// - 用户注册和信息管理
/// - 投注时的身份验证和余额扣除
/// - 上分下分的余额变动
/// - 回水返利的计算和发放
/// - 拉手层级的管理和返利
///
/// 数据库表：Member
/// - 使用Account作为主键，确保用户唯一性
/// - 支持复杂的用户查询和统计
/// - 关联BetOrder、Finance等业务表
///
/// 设计特点：
/// - 使用字符串主键，便于跨平台用户标识
/// - 支持中文字段名，贴近业务语义
/// - 完整的财务和层级关系支持
/// - 灵活的用户分类机制
///
/// 安全考虑：
/// - 余额字段使用decimal确保精度
/// - 支持假人用户的数据隔离
/// - 拉手关系的循环检测
/// </summary>
[Table(Name = "Member")]
public class Member
{
    /// <summary>
    /// 用户账号 - 用户在系统中的唯一标识
    ///
    /// 特点：
    /// - 字符串类型的主键
    /// - 全局唯一，不可重复
    /// - 通常对应聊天平台的用户ID
    ///
    /// 用途：
    /// - 用户身份的唯一标识
    /// - 所有业务操作的用户关联键
    /// - 投注订单的用户标识
    /// - 财务记录的账户标识
    ///
    /// 数据来源：
    /// - 微信：微信号或微信ID
    /// - QQ：QQ号码
    /// - 其他平台：对应的用户标识
    ///
    /// 重要性：
    /// - 系统用户管理的核心字段
    /// - 所有用户相关数据的关联键
    /// - 确保用户数据的一致性
    /// </summary>
    [Column(IsPrimary = true)]
    public string Account { get; set; } = string.Empty;

    /// <summary>
    /// 用户昵称 - 用户在聊天平台上的显示名称
    ///
    /// 用途：
    /// - 用户界面中的友好显示名称
    /// - @用户时使用的显示名称
    /// - 日志记录中的可读标识
    /// - 客服处理时的用户识别
    ///
    /// 特点：
    /// - 可能包含中文、英文、特殊字符
    /// - 在不同平台可能有不同格式
    /// - 比账号更具可读性和识别性
    ///
    /// 数据来源：
    /// - 从聊天平台API获取
    /// - 用户在平台上设置的昵称
    /// - 可能会随用户修改而变化
    ///
    /// 业务价值：
    /// - 提升用户体验的友好标识
    /// - 便于管理员识别和管理用户
    /// - 增强系统的人性化交互
    /// </summary>
    public string 昵称 { get; set; } = string.Empty;

    /// <summary>
    /// 备注名 - 管理员为用户设置的内部备注名称
    ///
    /// 用途：
    /// - 管理员内部管理用户的备注信息
    /// - 便于识别和记忆特定用户
    /// - 客服处理时的辅助信息
    /// - 用户分类和标记
    ///
    /// 特点：
    /// - 仅供内部使用，用户不可见
    /// - 可以包含任意描述信息
    /// - 便于管理员快速识别用户
    ///
    /// 使用场景：
    /// - 标记VIP用户或特殊用户
    /// - 记录用户的特殊情况
    /// - 便于客服快速了解用户背景
    ///
    /// 示例：
    /// - "VIP用户"
    /// - "老客户"
    /// - "需要特别关注"
    /// </summary>
    public string 备注名 { get; set; } = string.Empty;

    /// <summary>
    /// 账户余额 - 用户当前的可用资金余额
    ///
    /// 数据类型：
    /// - decimal类型确保金额精度
    /// - 通常精确到小数点后2位
    /// - 避免浮点数精度问题
    ///
    /// 业务规则：
    /// - 投注时扣除相应金额
    /// - 中奖时增加奖金
    /// - 上分时增加余额
    /// - 下分时减少余额
    /// - 回水时增加返还金额
    ///
    /// 安全控制：
    /// - 不能为负数（投注时检查余额）
    /// - 所有变动都有财务记录
    /// - 支持余额冻结和解冻
    ///
    /// 关联操作：
    /// - 每次变动生成Finance记录
    /// - 与BetOrder关联进行结算
    /// - 与AddMoney/SubMoney关联处理上下分
    ///
    /// 重要性：
    /// - 用户财务状态的核心数据
    /// - 投注业务的基础依据
    /// - 系统盈利的重要指标
    /// </summary>
    public decimal Balance { get; set; }

    /// <summary>
    /// 回水比例 - 用户投注的回水返还百分比
    ///
    /// 定义：
    /// - 用户投注后系统返还的比例
    /// - 通常为投注金额的一定百分比
    /// - 用于提高用户粘性和活跃度
    ///
    /// 数值范围：
    /// - 通常为0-10%之间的小数
    /// - 如0.05表示5%的回水比例
    /// - 不同用户可设置不同比例
    ///
    /// 计算公式：
    /// - 回水金额 = 投注金额 × 回水比例
    /// - 回水通常在投注后定期发放
    ///
    /// 业务意义：
    /// - 降低用户的投注成本
    /// - 提高用户的投注积极性
    /// - 增强用户对平台的忠诚度
    ///
    /// 管理方式：
    /// - 管理员可为不同用户设置不同比例
    /// - 支持VIP用户的差异化回水
    /// - 可根据用户活跃度调整
    /// </summary>
    public decimal 回水比例 { get; set; }

    /// <summary>
    /// 是否假人 - 标识用户是真实用户还是系统模拟用户
    ///
    /// 用途：
    /// - 区分真实用户和系统模拟用户
    /// - 统计分析时分别处理
    /// - 财务结算时区分真假数据
    ///
    /// 业务场景：
    /// - true：系统模拟的假人用户
    /// - false：真实的用户
    ///
    /// 假人用户的作用：
    /// - 增加群聊的活跃度
    /// - 模拟真实的投注环境
    /// - 测试系统功能
    /// - 数据分析和统计
    ///
    /// 数据隔离：
    /// - 假人数据不影响真实财务统计
    /// - 支持分别查询真人和假人数据
    /// - 便于系统测试和调试
    ///
    /// 管理控制：
    /// - 只有管理员可以设置
    /// - 用户无法看到此标识
    /// - 系统内部使用的分类标记
    /// </summary>
    public bool 是否假人 { get; set; }

    /// <summary>
    /// 拉手上级 - 推荐该用户的上级用户账号
    ///
    /// 拉手系统说明：
    /// - 拉手：推荐新用户加入系统的推广者
    /// - 上级：可以从下级用户的投注中获得返利
    /// - 层级关系：支持多级拉手关系
    ///
    /// 业务规则：
    /// - 存储上级用户的Account
    /// - 不能形成循环拉手关系
    /// - 一个用户只能有一个直接上级
    ///
    /// 返利机制：
    /// - 下级用户投注时，上级获得返利
    /// - 返利比例由"返利比例"字段控制
    /// - 返利金额直接增加到上级余额
    ///
    /// 关系管理：
    /// - 支持拉手关系的建立和修改
    /// - 防止循环引用的检查
    /// - 支持拉手层级的查询和统计
    ///
    /// 空值含义：
    /// - 空字符串表示该用户没有拉手上级
    /// - 通常为系统的初始用户或管理员
    /// </summary>
    public string ParentAccount { get; set; } = string.Empty;

    /// <summary>
    /// 返利比例 - 上级从该用户投注中获得的返利百分比
    ///
    /// 定义：
    /// - 该用户投注时，其拉手上级获得的返利比例
    /// - 用于激励用户推广和维护下级用户
    /// - 构建多级返利的推广体系
    ///
    /// 数值范围：
    /// - 通常为0-2.5%之间的小数
    /// - 如0.01表示1%的返利比例
    /// - 默认值为0，表示无返利
    ///
    /// 计算公式：
    /// - 返利金额 = 下级投注金额 × 返利比例
    /// - 返利直接增加到上级用户余额
    ///
    /// 业务流程：
    /// 1. 下级用户投注
    /// 2. 系统计算返利金额
    /// 3. 增加上级用户余额
    /// 4. 生成返利财务记录
    ///
    /// 管理控制：
    /// - 管理员可设置不同的返利比例
    /// - 支持差异化的返利政策
    /// - 可根据推广效果调整比例
    ///
    /// 重要性：
    /// - 推广体系的核心激励机制
    /// - 用户裂变增长的重要工具
    /// - 平台生态建设的关键要素
    /// </summary>
    public decimal ParentRebatePercent { get; set; } = 0;
}