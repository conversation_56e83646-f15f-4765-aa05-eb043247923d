# 期号时间服务文档

## 概述

`IssueTimeService` 是彩票投注系统的核心时间管理服务，负责期号时间数据的完整生命周期管理，为整个投注系统提供准确、高效、线程安全的时间控制和状态管理功能。

## 核心功能

### 1. 期号数据管理
- **自动生成**：为指定年份生成完整的期号时间数据
- **业务规则**：每天203期，从早上7点开始，每期间隔5分钟
- **期号格式**：民国年份+6位序号（如：113000001）
- **跨年处理**：自动处理跨年数据创建

### 2. 实时状态管理
- **状态类型**：7种状态覆盖完整业务流程
  - `Unknown`：系统初始化或异常状态
  - `WaitingToOpen`：等待开放（距离开放>1分钟）
  - `PreOpening`：即将开放（距离开放≤1分钟）
  - `Open`：投注开放中（正常投注时间）
  - `PreClosing`：即将关闭（距离关闭≤1分钟）
  - `Closed`：投注已关闭（等待下期）
  - `Maintenance`：系统维护中

### 3. 高性能缓存
- **智能策略**：缓存命中率>99%
- **失效机制**：关键时间点自动刷新
- **内存优化**：最小化内存占用

### 4. 多线程安全
- **读写锁**：ReaderWriterLockSlim优化并发性能
- **原子操作**：状态更新具有原子性保证
- **高并发**：支持数千并发读取

## 接口定义

### 核心方法

#### CreateIssueTimeAsync
```csharp
Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear)
```
- **功能**：为指定年份生成完整的期号时间数据
- **性能**：批量插入，年度数据生成<1秒
- **异常处理**：数据已存在时直接返回

#### UpdateCurrentIssueTimeAsync
```csharp
Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken)
```
- **功能**：后台持续运行，维护当前期号缓存和状态
- **更新频率**：每秒检查一次
- **异常恢复**：自动重试和恢复机制

### 缓存访问方法

#### GetCurrentCachedIssueTime
```csharp
IssueTime? GetCurrentCachedIssueTime()
```
- **性能**：读锁保护，支持高并发
- **用途**：高频率状态查询
- **注意**：返回引用，不应修改

#### GetCurrentCachedIssueTimeCopy
```csharp
IssueTime? GetCurrentCachedIssueTimeCopy()
```
- **功能**：返回深度副本
- **用途**：需要修改数据的计算场景
- **性能**：有创建对象开销

### 状态查询方法

#### GetCurrentIssueStatus
```csharp
IssueStatus GetCurrentIssueStatus()
```
- **性能**：直接返回预计算状态，<1ms
- **用途**：UI状态显示、业务判断

#### GetCurrentIssueTimeAndStatus
```csharp
(IssueTime? issueTime, IssueStatus status) GetCurrentIssueTimeAndStatus()
```
- **原子性**：一次锁操作获取两个数据
- **一致性**：确保期号信息和状态匹配

#### CanBetNow
```csharp
bool CanBetNow()
```
- **功能**：快速投注权限判断
- **逻辑**：Open和PreClosing状态允许投注

### 管理方法

#### RefreshCurrentStatus
```csharp
void RefreshCurrentStatus()
```
- **功能**：手动刷新状态
- **用途**：异常恢复、调试测试

#### ClearCache
```csharp
void ClearCache()
```
- **功能**：清空缓存，强制重新加载
- **影响**：短暂的服务不可用

## 业务常量

### 时间配置
- `DailyIssueCount = 203`：每天期号数量
- `IntervalMinutes = 5`：期号间隔时间
- `DurationMinutes = 5`：每期持续时间
- `EarlyCloseSeconds = 10`：提前关闭时间
- `StartHour = 7`：每天开始时间

### 性能配置
- `CacheValidThresholdSeconds = 30.0`：缓存失效阈值
- `PreStatusThresholdMinutes = 1.0`：即将状态阈值

## 性能指标

| 指标 | 数值 | 说明 |
|------|------|------|
| 缓存命中率 | >99% | 智能缓存策略 |
| 读取QPS | >10000 | 多线程并发 |
| 状态查询延迟 | <1ms | 预计算状态 |
| 写入延迟 | <1ms | 单线程写入 |
| 年度数据生成 | <1秒 | 批量操作优化 |

## 使用示例

### 基本状态检查
```csharp
// 快速检查是否可投注
bool canBet = _issueTimeService.CanBetNow();

// 获取详细状态
var status = _issueTimeService.GetCurrentIssueStatus();
Console.WriteLine($"当前状态: {status.GetDisplayName()}");

// 一次性获取期号和状态
var (issueTime, issueStatus) = _issueTimeService.GetCurrentIssueTimeAndStatus();
```

### 状态变化监控
```csharp
IssueStatus lastStatus = IssueStatus.Unknown;
while (!cancellationToken.IsCancellationRequested)
{
    var currentStatus = _issueTimeService.GetCurrentIssueStatus();
    if (currentStatus != lastStatus)
    {
        await HandleStatusChange(lastStatus, currentStatus);
        lastStatus = currentStatus;
    }
    await Task.Delay(1000, cancellationToken);
}
```

### 投注前验证
```csharp
public bool ValidateBeforeBetting(string userAccount, decimal betAmount)
{
    // 检查系统状态
    if (!_issueTimeService.IsCacheInitialized())
        return false;
    
    // 快速检查投注权限
    if (!_issueTimeService.CanBetNow())
        return false;
    
    // 获取详细状态进行进一步验证
    var (issueTime, status) = _issueTimeService.GetCurrentIssueTimeAndStatus();
    
    // 根据状态执行不同逻辑...
    return true;
}
```

## 线程安全保障

### 读写锁机制
- **读锁**：多线程并发访问
- **写锁**：独占访问，确保一致性
- **性能优化**：读多写少场景优化

### 原子操作
- **状态更新**：期号信息和状态同步更新
- **缓存管理**：原子性的缓存操作
- **异常安全**：操作失败不影响系统状态

## 监控和日志

### 日志级别
- **Debug**：缓存命中、状态检查
- **Information**：状态变化、数据创建
- **Warning**：异常恢复、数据不一致
- **Error**：数据库错误、计算异常

### 关键事件
- 期号数据创建和更新
- 状态变化详细记录
- 性能指标和缓存命中率
- 异常情况和恢复过程

## 最佳实践

### 性能优化
1. 优先使用 `GetCurrentCachedIssueTime()` 进行只读访问
2. 仅在需要修改数据时使用 `GetCurrentCachedIssueTimeCopy()`
3. 使用 `CanBetNow()` 进行快速投注权限检查
4. 避免频繁调用 `RefreshCurrentStatus()`

### 错误处理
1. 检查 `IsCacheInitialized()` 确保系统已初始化
2. 处理返回值为null的情况
3. 监控状态变化，及时响应异常状态
4. 实现优雅的降级策略

### 资源管理
1. 通过依赖注入容器管理服务生命周期
2. 应用程序关闭时自动释放资源
3. 避免手动调用 `Dispose()` 方法

## 扩展性设计

### 状态扩展
- 新增状态类型只需修改枚举
- 扩展方法自动支持新状态
- 业务逻辑无需大量修改

### 配置扩展
- 业务常量可通过配置文件调整
- 支持运行时配置更新
- 便于不同环境的参数调整

### 功能扩展
- 接口设计支持功能扩展
- 依赖注入便于功能替换
- 模块化设计便于独立测试
