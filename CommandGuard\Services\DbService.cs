﻿using CommandGuard.Configuration;
using CommandGuard.Interfaces;
using FreeSql;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace CommandGuard.Services;

/// <summary>
/// 数据库服务实现类
/// 负责管理FreeSql ORM实例的生命周期和配置
/// 实现了IDisposable接口，确保资源的正确释放
/// 使用依赖注入获取配置和日志服务，提高可测试性
/// 采用单例模式注册，确保整个应用程序共享同一个数据库连接池
/// </summary>
public class DbService : IDbService, IDisposable
{
    #region 私有字段

    /// <summary>
    /// FreeSql ORM 实例
    /// 负责所有数据库操作，包括连接管理、SQL生成、结果映射等
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 数据库操作同步锁
    /// 确保多线程环境下的数据库操作安全
    /// </summary>
    private readonly object _dbLock;

    /// <summary>
    /// 日志记录器
    /// 用于记录数据库服务的初始化、操作和错误信息
    /// </summary>
    private readonly ILogger<DbService> _logger;

    /// <summary>
    /// 资源释放标志
    /// 防止重复释放资源，实现IDisposable模式
    /// </summary>
    private bool _disposed;

    #endregion

    #region 构造函数

    /// <summary>
    /// 数据库服务构造函数
    /// 通过依赖注入获取配置和日志服务，初始化FreeSql实例
    /// </summary>
    /// <param name="options">应用程序配置选项，包含数据库连接字符串等设置</param>
    /// <param name="logger">日志记录器，用于记录服务运行状态</param>
    /// <exception cref="ArgumentNullException">当参数为null时抛出</exception>
    public DbService(IOptions<AppSettings> options, ILogger<DbService> logger)
    {
        // 参数验证，确保依赖注入的服务不为空
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        var settings = options.Value ?? throw new ArgumentNullException(nameof(options));

        // 初始化同步锁对象
        _dbLock = new object();

        _logger.LogInformation("正在初始化数据库服务...");

        try
        {
            // 创建并配置FreeSql实例
            _freeSql = new FreeSqlBuilder()
                .UseConnectionString(DataType.Sqlite, settings.ConnectionStrings.DefaultConnection)
                .UseAutoSyncStructure(settings.Application.EnableAutoSyncStructure) // 根据配置决定是否自动同步数据库结构
                .Build(); // 构建FreeSql实例，注意：应该作为单例使用

            _logger.LogInformation("数据库服务初始化完成，连接字符串：{ConnectionString}",
                settings.ConnectionStrings.DefaultConnection);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "数据库服务初始化失败");
            throw; // 重新抛出异常，让调用方知道初始化失败
        }
    }

    #endregion

    #region 公共属性

    /// <summary>
    /// 获取FreeSql ORM实例
    /// 提供对数据库的完整访问能力，包括查询、插入、更新、删除等操作
    /// 支持LINQ查询、原生SQL、存储过程等多种数据访问方式
    /// </summary>
    /// <returns>配置好的FreeSql实例</returns>
    public IFreeSql FreeSql => _freeSql;

    /// <summary>
    /// 获取数据库操作同步锁对象
    /// 在需要确保数据库操作原子性或避免并发冲突时使用
    /// 特别适用于SQLite等不支持高并发写入的数据库
    /// </summary>
    /// <returns>用于同步的锁对象</returns>
    public object DbLock => _dbLock;

    #endregion

    #region 资源释放

    /// <summary>
    /// 释放数据库服务占用的所有资源
    /// 实现IDisposable接口，确保FreeSql实例和相关资源得到正确释放
    /// 调用此方法后，不应再使用此服务实例
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this); // 告诉GC不需要调用终结器
    }

    /// <summary>
    /// 受保护的资源释放方法
    /// 实现标准的Dispose模式，防止重复释放资源
    /// </summary>
    /// <param name="disposing">
    /// true: 正在从Dispose方法调用，需要释放托管和非托管资源
    /// false: 正在从终结器调用，只能释放非托管资源
    /// </param>
    protected virtual void Dispose(bool disposing)
    {
        // 检查是否已经释放过资源，避免重复释放
        if (!_disposed && disposing)
        {
            _logger.LogInformation("正在释放数据库服务资源...");

            try
            {
                // 释放FreeSql实例，这会关闭所有数据库连接
                _freeSql.Dispose();
                _logger.LogInformation("数据库服务资源释放完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放数据库服务资源时发生错误");
            }
            finally
            {
                _disposed = true; // 标记为已释放
            }
        }
    }

    #endregion
}