namespace CommandGuard.Configuration;

/// <summary>
/// 应用程序配置根类
/// 用于映射 appsettings.json 配置文件的结构
/// 通过强类型配置绑定，提供类型安全的配置访问方式
/// 支持配置文件的热重载和验证
/// </summary>
public class AppSettings
{
    /// <summary>
    /// 数据库连接字符串配置节
    /// 包含应用程序所需的各种数据库连接信息
    /// 支持多个数据库连接配置，便于不同环境的部署
    /// </summary>
    public ConnectionStrings ConnectionStrings { get; set; } = new();

    /// <summary>
    /// 应用程序特定配置节
    /// 包含应用程序运行时的各种设置参数
    /// 如应用名称、版本信息、功能开关等
    /// </summary>
    public ApplicationConfig Application { get; set; } = new();
}

/// <summary>
/// 数据库连接字符串配置类
/// 用于管理应用程序的数据库连接信息
/// 支持多个数据库连接，便于复杂应用场景
/// </summary>
public class ConnectionStrings
{
    /// <summary>
    /// 默认数据库连接字符串
    /// 当前使用SQLite数据库，包含数据库文件路径和连接池配置
    /// 格式：Data Source=数据库文件路径;其他参数
    /// 示例：Data Source=Robot.db;Version=3;Pooling=true;Max Pool Size=100;Min Pool Size=5;
    /// </summary>
    public string DefaultConnection { get; set; } = string.Empty;
}

/// <summary>
/// 应用程序特定配置类
/// 包含应用程序运行时的各种设置和功能开关
/// 这些配置可以在不重新编译的情况下进行调整
/// </summary>
public class ApplicationConfig
{
    /// <summary>
    /// 应用程序显示名称
    /// 用于在界面标题、日志记录、关于对话框等地方显示
    /// 可以包含中文字符和特殊符号
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 应用程序版本号
    /// 遵循语义化版本控制规范（如：1.0.0）
    /// 用于版本管理、更新检查、兼容性判断等
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 是否启用数据库结构自动同步功能
    /// true：FreeSql会自动根据实体类创建或更新数据库表结构
    /// false：需要手动管理数据库表结构
    /// 生产环境建议设置为false，开发环境可设置为true
    /// </summary>
    public bool EnableAutoSyncStructure { get; set; } = true;
}