﻿using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class MemberService : IMemberService
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    ///
    /// 功能：记录服务运行过程中的详细信息
    /// 日志级别：
    /// - Debug：缓存命中、状态检查等调试信息
    /// - Information：状态变化、数据创建等重要操作
    /// - Warning：异常恢复、数据不一致等警告
    /// - Error：数据库错误、计算异常等错误信息
    ///
    /// 日志内容：
    /// - 期号数据的创建和更新
    /// - 状态变化的详细记录
    /// - 性能指标和缓存命中率
    /// - 异常情况和恢复过程
    /// </summary>
    private readonly ILogger<MemberService> _logger;

    /// <summary>
    /// 数据库服务接口
    ///
    /// 功能：提供数据库访问能力和连接管理
    /// 核心能力：
    /// - FreeSql ORM实例访问
    /// - 数据库连接池管理
    /// - 事务处理支持
    /// - 线程安全的数据库操作锁
    ///
    /// 使用场景：
    /// - 期号数据的批量创建和查询
    /// - 数据库结构的自动同步
    /// - 复杂查询和数据统计
    /// - 数据库连接异常的处理
    /// </summary>
    private readonly IDbService _dbService;

    #endregion

    #region 构造函数

    /// <summary>
    /// 人员服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <param name="dbService"></param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public MemberService(ILogger<MemberService> logger, IDbService dbService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
    }

    #endregion

    #region 公共方法

    public Task<int> AddMemberAsync(Member member)
    {
        _logger.LogInformation("添加成员：{0}", member.Account);
        return _dbService.FreeSql.Insert(member).AppendData(member).ExecuteAffrowsAsync();
    }

    public Task<Member> GetMemberByAccountAsync(string account)
    {
        _logger.LogDebug("查询成员：{0}", account);
        return _dbService.FreeSql.Select<Member>()
            .Where(a => a.Account == account)
            .FirstAsync();
    }

    public Task<List<Member>> GetAllMemberAsync()
    {
        _logger.LogDebug("查询所有成员");
        return _dbService.FreeSql.Select<Member>().ToListAsync();
    }

    #endregion
}