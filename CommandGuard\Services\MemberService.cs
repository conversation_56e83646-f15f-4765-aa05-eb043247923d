﻿using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

public class MemberService : IMemberService
{
    #region 私有字段

    /// <summary>
    /// FreeSql ORM实例
    /// 用于执行数据库CRUD操作，提供强类型的数据访问能力
    /// </summary>
    private readonly IFreeSql _freeSql;

    /// <summary>
    /// 日志记录器
    /// 记录业务操作的详细信息，便于调试和监控
    /// </summary>
    private readonly ILogger<MemberService> _logger;

    #endregion

    #region 构造函数

    /// <summary>
    /// 人员服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="freeSql">FreeSql ORM实例，用于数据访问</param>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public MemberService(IFreeSql freeSql, ILogger<MemberService> logger)
    {
        _freeSql = freeSql ?? throw new ArgumentNullException(nameof(freeSql));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region 公共方法

    public Task<int> AddMemberAsync(Member member)
    {
        return _freeSql.Insert(member).AppendData(member).ExecuteAffrowsAsync();
    }

    public Task<Member> GetMemberByQqAsync(string qq)
    {
        return _freeSql.Select<Member>()
            .Where(a => a.Account == qq)
            .FirstAsync();
    }

    public Task<List<Member>> GetAllMemberAsync()
    {
        return _freeSql.Select<Member>().ToListAsync();
    }

    #endregion
}