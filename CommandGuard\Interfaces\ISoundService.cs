using System.Media;

namespace CommandGuard.Interfaces;

/// <summary>
/// 音效服务接口
/// 定义音效播放和管理的核心功能
/// 
/// 核心职责：
/// 1. 音效资源管理 - 加载和管理各种音效文件
/// 2. 音效播放控制 - 提供不同场景的音效播放功能
/// 3. 音效状态管理 - 管理音效播放器的生命周期
/// 
/// 支持的音效类型：
/// - 系统提示音：用于一般的系统操作反馈
/// - 警告音效：用于重要提醒和警告
/// - 成功音效：用于操作成功的反馈
/// - 错误音效：用于错误和异常的提醒
/// 
/// 设计特点：
/// - 资源预加载：系统启动时预加载所有音效文件
/// - 异步播放：音效播放不阻塞主线程
/// - 资源管理：自动管理音效播放器的生命周期
/// - 异常安全：音效播放失败不影响主系统运行
/// </summary>
public interface ISoundService
{
    #region 属性 - 音效播放器管理

    /// <summary>
    /// 系统提示音播放器
    /// 
    /// 功能：播放一般的系统操作提示音
    /// 
    /// 使用场景：
    /// - 按钮点击反馈
    /// - 菜单操作提示
    /// - 一般的用户交互反馈
    /// - 非关键性的系统通知
    /// 
    /// 音效特点：
    /// - 音量适中，不会打扰用户
    /// - 时长较短，通常1-2秒
    /// - 音调平和，适合频繁播放
    /// </summary>
    SoundPlayer? SystemSoundPlayer { get; }

    /// <summary>
    /// 警告音效播放器
    /// 
    /// 功能：播放重要提醒和警告音效
    /// 
    /// 使用场景：
    /// - 封盘提醒：投注即将截止时的警告
    /// - 开奖提醒：开奖时间到达的通知
    /// - 系统异常：重要错误的警告提示
    /// - 资金变动：重要的资金操作提醒
    /// 
    /// 音效特点：
    /// - 音量较大，确保用户注意
    /// - 音调较高，具有警示性
    /// - 可能重复播放，强调重要性
    /// </summary>
    SoundPlayer? WarningSoundPlayer { get; }

    /// <summary>
    /// 成功音效播放器
    /// 
    /// 功能：播放操作成功的反馈音效
    /// 
    /// 使用场景：
    /// - 投注成功：用户投注操作成功
    /// - 登录成功：用户登录系统成功
    /// - 数据保存：重要数据保存成功
    /// - 任务完成：后台任务执行成功
    /// 
    /// 音效特点：
    /// - 音调愉悦，给用户正面反馈
    /// - 时长适中，不会过于突兀
    /// - 音量适中，提供确认感
    /// </summary>
    SoundPlayer? SuccessSoundPlayer { get; }

    /// <summary>
    /// 错误音效播放器
    /// 
    /// 功能：播放错误和异常的提醒音效
    /// 
    /// 使用场景：
    /// - 投注失败：用户投注操作失败
    /// - 验证错误：输入数据验证失败
    /// - 网络异常：网络连接或数据获取失败
    /// - 系统错误：系统运行时发生错误
    /// 
    /// 音效特点：
    /// - 音调低沉，表示错误状态
    /// - 时长较短，避免过度打扰
    /// - 音量适中，提醒但不刺耳
    /// </summary>
    SoundPlayer? ErrorSoundPlayer { get; }

    #endregion

    #region 方法 - 核心业务接口

    /// <summary>
    /// 初始化音效服务
    /// 
    /// 功能：加载所有音效文件并初始化播放器
    /// 
    /// 执行流程：
    /// 1. 检查音效文件是否存在
    /// 2. 创建对应的SoundPlayer实例
    /// 3. 预加载音效文件到内存
    /// 4. 验证音效文件的有效性
    /// 5. 记录初始化结果
    /// 
    /// 文件路径约定：
    /// - 系统提示音：Resources/Sounds/system.wav
    /// - 警告音效：Resources/Sounds/warning.wav
    /// - 成功音效：Resources/Sounds/success.wav
    /// - 错误音效：Resources/Sounds/error.wav
    /// 
    /// 异常处理：
    /// - 单个音效文件加载失败不影响其他音效
    /// - 记录详细的错误信息便于问题排查
    /// - 初始化失败时设置对应播放器为null
    /// 
    /// 性能考虑：
    /// - 预加载音效文件，避免播放时的延迟
    /// - 异步初始化，不阻塞主线程
    /// - 资源管理，避免内存泄漏
    /// 
    /// 调用时机：
    /// - 系统启动时调用一次
    /// - 音效设置更改后重新初始化
    /// - 音效文件更新后重新加载
    /// </summary>
    /// <returns>异步任务</returns>
    Task InitializeSoundServiceAsync();

    /// <summary>
    /// 播放系统提示音
    /// 
    /// 功能：播放一般的系统操作提示音
    /// 
    /// 播放特点：
    /// - 异步播放，不阻塞调用线程
    /// - 如果当前正在播放，则停止后重新播放
    /// - 播放失败时记录日志但不抛出异常
    /// 
    /// 使用场景：
    /// - 按钮点击反馈
    /// - 菜单操作提示
    /// - 一般的用户交互反馈
    /// - 非关键性的系统通知
    /// 
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    Task PlaySystemSoundAsync();

    /// <summary>
    /// 播放警告音效
    /// 
    /// 功能：播放重要提醒和警告音效
    /// 
    /// 播放特点：
    /// - 音量较大，确保用户注意
    /// - 可能重复播放，强调重要性
    /// - 优先级较高，可能中断其他音效
    /// 
    /// 使用场景：
    /// - 封盘提醒：投注即将截止时的警告
    /// - 开奖提醒：开奖时间到达的通知
    /// - 系统异常：重要错误的警告提示
    /// - 资金变动：重要的资金操作提醒
    /// 
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    Task PlayWarningSoundAsync();

    /// <summary>
    /// 播放成功音效
    /// 
    /// 功能：播放操作成功的反馈音效
    /// 
    /// 播放特点：
    /// - 音调愉悦，给用户正面反馈
    /// - 时长适中，不会过于突兀
    /// - 音量适中，提供确认感
    /// 
    /// 使用场景：
    /// - 投注成功：用户投注操作成功
    /// - 登录成功：用户登录系统成功
    /// - 数据保存：重要数据保存成功
    /// - 任务完成：后台任务执行成功
    /// 
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    Task PlaySuccessSoundAsync();

    /// <summary>
    /// 播放错误音效
    /// 
    /// 功能：播放错误和异常的提醒音效
    /// 
    /// 播放特点：
    /// - 音调低沉，表示错误状态
    /// - 时长较短，避免过度打扰
    /// - 音量适中，提醒但不刺耳
    /// 
    /// 使用场景：
    /// - 投注失败：用户投注操作失败
    /// - 验证错误：输入数据验证失败
    /// - 网络异常：网络连接或数据获取失败
    /// - 系统错误：系统运行时发生错误
    /// 
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    Task PlayErrorSoundAsync();

    /// <summary>
    /// 停止所有音效播放
    /// 
    /// 功能：立即停止所有正在播放的音效
    /// 
    /// 使用场景：
    /// - 系统关闭时清理资源
    /// - 用户禁用音效时停止播放
    /// - 紧急情况下停止所有声音
    /// - 切换音效设置时重置状态
    /// 
    /// 执行逻辑：
    /// - 遍历所有音效播放器
    /// - 调用Stop()方法停止播放
    /// - 不释放播放器资源，保持可重用状态
    /// - 记录停止操作的日志
    /// 
    /// 异常处理：
    /// - 单个播放器停止失败不影响其他播放器
    /// - 记录详细的错误信息
    /// - 确保方法执行完成，不抛出异常
    /// </summary>
    /// <returns>异步任务</returns>
    Task StopAllSoundsAsync();

    /// <summary>
    /// 释放音效服务资源
    /// 
    /// 功能：释放所有音效播放器和相关资源
    /// 
    /// 使用场景：
    /// - 系统关闭时清理资源
    /// - 音效服务重新初始化前清理
    /// - 内存清理和垃圾回收
    /// 
    /// 执行逻辑：
    /// - 停止所有正在播放的音效
    /// - 释放所有SoundPlayer实例
    /// - 清理预加载的音效数据
    /// - 重置所有播放器属性为null
    /// 
    /// 异常处理：
    /// - 单个播放器释放失败不影响其他播放器
    /// - 记录详细的错误信息
    /// - 确保方法执行完成，不抛出异常
    /// 
    /// 注意事项：
    /// - 调用此方法后需要重新初始化才能播放音效
    /// - 建议在应用程序关闭时调用
    /// - 实现IDisposable模式的一部分
    /// </summary>
    /// <returns>异步任务</returns>
    Task DisposeSoundServiceAsync();

    #endregion
}
