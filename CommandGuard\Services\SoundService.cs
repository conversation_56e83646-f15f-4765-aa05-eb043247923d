using System.Media;
using CommandGuard.Interfaces;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 音效服务实现类
/// 负责音效播放和管理的具体实现
/// 
/// 这是整个机器人系统的音效管理核心，负责加载、管理和播放各种音效文件，
/// 为用户提供丰富的音频反馈体验。
/// 
/// 核心职责：
/// 1. 音效资源管理 - 加载和管理各种音效文件
/// 2. 音效播放控制 - 提供不同场景的音效播放功能
/// 3. 音效状态管理 - 管理音效播放器的生命周期
/// 
/// 支持的音效类型：
/// - 系统提示音：用于一般的系统操作反馈
/// - 警告音效：用于重要提醒和警告
/// - 成功音效：用于操作成功的反馈
/// - 错误音效：用于错误和异常的提醒
/// 
/// 设计特点：
/// - 资源预加载：系统启动时预加载所有音效文件
/// - 异步播放：音效播放不阻塞主线程
/// - 资源管理：自动管理音效播放器的生命周期
/// - 异常安全：音效播放失败不影响主系统运行
/// </summary>
public class SoundService : ISoundService, IDisposable
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 记录音效操作的详细信息，便于调试和监控
    /// </summary>
    private readonly ILogger<SoundService> _logger;

    /// <summary>
    /// 资源释放标志
    /// 标识当前实例是否已被释放，避免重复释放
    /// </summary>
    private bool _disposed = false;

    #endregion

    #region 音效文件路径常量

    /// <summary>
    /// 音效文件路径配置
    /// 
    /// 功能：集中管理所有音效文件的路径配置
    /// 
    /// 路径约定：
    /// - 所有音效文件存放在Resources/Sounds目录下
    /// - 使用WAV格式确保兼容性和音质
    /// - 文件名采用英文小写，语义清晰
    /// 
    /// 扩展性：
    /// - 新增音效类型只需添加路径配置
    /// - 支持相对路径和绝对路径
    /// - 便于音效文件的统一管理
    /// </summary>
    private static class SoundPaths
    {
        /// <summary>音效文件根目录</summary>
        public const string SoundsDirectory = "Resources/Sounds";

        /// <summary>系统提示音文件路径</summary>
        public static readonly string SystemSound = Path.Combine(SoundsDirectory, "system.wav");

        /// <summary>警告音效文件路径</summary>
        public static readonly string WarningSound = Path.Combine(SoundsDirectory, "warning.wav");

        /// <summary>成功音效文件路径</summary>
        public static readonly string SuccessSound = Path.Combine(SoundsDirectory, "success.wav");

        /// <summary>错误音效文件路径</summary>
        public static readonly string ErrorSound = Path.Combine(SoundsDirectory, "error.wav");
    }

    #endregion

    #region 构造函数

    /// <summary>
    /// 音效服务构造函数
    /// 通过依赖注入获取所需的服务实例
    /// </summary>
    /// <param name="logger">日志记录器，用于记录操作日志</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public SoundService(ILogger<SoundService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    #endregion

    #region 公共属性 - 音效播放器管理

    /// <summary>
    /// 系统提示音播放器
    /// 
    /// 功能：播放一般的系统操作提示音
    /// 
    /// 使用场景：
    /// - 按钮点击反馈
    /// - 菜单操作提示
    /// - 一般的用户交互反馈
    /// - 非关键性的系统通知
    /// 
    /// 音效特点：
    /// - 音量适中，不会打扰用户
    /// - 时长较短，通常1-2秒
    /// - 音调平和，适合频繁播放
    /// </summary>
    public SoundPlayer? SystemSoundPlayer { get; private set; }

    /// <summary>
    /// 警告音效播放器
    /// 
    /// 功能：播放重要提醒和警告音效
    /// 
    /// 使用场景：
    /// - 封盘提醒：投注即将截止时的警告
    /// - 开奖提醒：开奖时间到达的通知
    /// - 系统异常：重要错误的警告提示
    /// - 资金变动：重要的资金操作提醒
    /// 
    /// 音效特点：
    /// - 音量较大，确保用户注意
    /// - 音调较高，具有警示性
    /// - 可能重复播放，强调重要性
    /// </summary>
    public SoundPlayer? WarningSoundPlayer { get; private set; }

    /// <summary>
    /// 成功音效播放器
    /// 
    /// 功能：播放操作成功的反馈音效
    /// 
    /// 使用场景：
    /// - 投注成功：用户投注操作成功
    /// - 登录成功：用户登录系统成功
    /// - 数据保存：重要数据保存成功
    /// - 任务完成：后台任务执行成功
    /// 
    /// 音效特点：
    /// - 音调愉悦，给用户正面反馈
    /// - 时长适中，不会过于突兀
    /// - 音量适中，提供确认感
    /// </summary>
    public SoundPlayer? SuccessSoundPlayer { get; private set; }

    /// <summary>
    /// 错误音效播放器
    /// 
    /// 功能：播放错误和异常的提醒音效
    /// 
    /// 使用场景：
    /// - 投注失败：用户投注操作失败
    /// - 验证错误：输入数据验证失败
    /// - 网络异常：网络连接或数据获取失败
    /// - 系统错误：系统运行时发生错误
    /// 
    /// 音效特点：
    /// - 音调低沉，表示错误状态
    /// - 时长较短，避免过度打扰
    /// - 音量适中，提醒但不刺耳
    /// </summary>
    public SoundPlayer? ErrorSoundPlayer { get; private set; }

    #endregion

    #region 公共方法 - 对外提供的核心接口

    /// <summary>
    /// 初始化音效服务
    ///
    /// 功能：加载所有音效文件并初始化播放器
    ///
    /// 执行流程：
    /// 1. 检查音效文件是否存在
    /// 2. 创建对应的SoundPlayer实例
    /// 3. 预加载音效文件到内存
    /// 4. 验证音效文件的有效性
    /// 5. 记录初始化结果
    ///
    /// 文件路径约定：
    /// - 系统提示音：Resources/Sounds/system.wav
    /// - 警告音效：Resources/Sounds/warning.wav
    /// - 成功音效：Resources/Sounds/success.wav
    /// - 错误音效：Resources/Sounds/error.wav
    ///
    /// 异常处理：
    /// - 单个音效文件加载失败不影响其他音效
    /// - 记录详细的错误信息便于问题排查
    /// - 初始化失败时设置对应播放器为null
    ///
    /// 性能考虑：
    /// - 预加载音效文件，避免播放时的延迟
    /// - 异步初始化，不阻塞主线程
    /// - 资源管理，避免内存泄漏
    ///
    /// 调用时机：
    /// - 系统启动时调用一次
    /// - 音效设置更改后重新初始化
    /// - 音效文件更新后重新加载
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task InitializeSoundServiceAsync()
    {
        try
        {
            _logger.LogInformation("开始初始化音效服务");

            // 确保音效目录存在
            await EnsureSoundsDirectoryExists();

            // 初始化各种音效播放器
            SystemSoundPlayer = await LoadSoundPlayerAsync(SoundPaths.SystemSound, "系统提示音");
            WarningSoundPlayer = await LoadSoundPlayerAsync(SoundPaths.WarningSound, "警告音效");
            SuccessSoundPlayer = await LoadSoundPlayerAsync(SoundPaths.SuccessSound, "成功音效");
            ErrorSoundPlayer = await LoadSoundPlayerAsync(SoundPaths.ErrorSound, "错误音效");

            // 统计初始化结果
            var loadedCount = new[] { SystemSoundPlayer, WarningSoundPlayer, SuccessSoundPlayer, ErrorSoundPlayer }
                .Count(player => player != null);

            _logger.LogInformation("音效服务初始化完成，成功加载 {LoadedCount}/4 个音效文件", loadedCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "音效服务初始化时发生异常");
            // 不重新抛出异常，确保系统能够继续运行
        }
    }

    /// <summary>
    /// 播放系统提示音
    ///
    /// 功能：播放一般的系统操作提示音
    ///
    /// 播放特点：
    /// - 异步播放，不阻塞调用线程
    /// - 如果当前正在播放，则停止后重新播放
    /// - 播放失败时记录日志但不抛出异常
    ///
    /// 使用场景：
    /// - 按钮点击反馈
    /// - 菜单操作提示
    /// - 一般的用户交互反馈
    /// - 非关键性的系统通知
    ///
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task PlaySystemSoundAsync()
    {
        await PlaySoundAsync(SystemSoundPlayer, "系统提示音");
    }

    /// <summary>
    /// 播放警告音效
    ///
    /// 功能：播放重要提醒和警告音效
    ///
    /// 播放特点：
    /// - 音量较大，确保用户注意
    /// - 可能重复播放，强调重要性
    /// - 优先级较高，可能中断其他音效
    ///
    /// 使用场景：
    /// - 封盘提醒：投注即将截止时的警告
    /// - 开奖提醒：开奖时间到达的通知
    /// - 系统异常：重要错误的警告提示
    /// - 资金变动：重要的资金操作提醒
    ///
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task PlayWarningSoundAsync()
    {
        await PlaySoundAsync(WarningSoundPlayer, "警告音效");
    }

    /// <summary>
    /// 播放成功音效
    ///
    /// 功能：播放操作成功的反馈音效
    ///
    /// 播放特点：
    /// - 音调愉悦，给用户正面反馈
    /// - 时长适中，不会过于突兀
    /// - 音量适中，提供确认感
    ///
    /// 使用场景：
    /// - 投注成功：用户投注操作成功
    /// - 登录成功：用户登录系统成功
    /// - 数据保存：重要数据保存成功
    /// - 任务完成：后台任务执行成功
    ///
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task PlaySuccessSoundAsync()
    {
        await PlaySoundAsync(SuccessSoundPlayer, "成功音效");
    }

    /// <summary>
    /// 播放错误音效
    ///
    /// 功能：播放错误和异常的提醒音效
    ///
    /// 播放特点：
    /// - 音调低沉，表示错误状态
    /// - 时长较短，避免过度打扰
    /// - 音量适中，提醒但不刺耳
    ///
    /// 使用场景：
    /// - 投注失败：用户投注操作失败
    /// - 验证错误：输入数据验证失败
    /// - 网络异常：网络连接或数据获取失败
    /// - 系统错误：系统运行时发生错误
    ///
    /// 异常处理：
    /// - 播放器未初始化时跳过播放
    /// - 音效文件损坏时记录错误日志
    /// - 播放异常不影响主系统运行
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task PlayErrorSoundAsync()
    {
        await PlaySoundAsync(ErrorSoundPlayer, "错误音效");
    }

    /// <summary>
    /// 停止所有音效播放
    ///
    /// 功能：立即停止所有正在播放的音效
    ///
    /// 使用场景：
    /// - 系统关闭时清理资源
    /// - 用户禁用音效时停止播放
    /// - 紧急情况下停止所有声音
    /// - 切换音效设置时重置状态
    ///
    /// 执行逻辑：
    /// - 遍历所有音效播放器
    /// - 调用Stop()方法停止播放
    /// - 不释放播放器资源，保持可重用状态
    /// - 记录停止操作的日志
    ///
    /// 异常处理：
    /// - 单个播放器停止失败不影响其他播放器
    /// - 记录详细的错误信息
    /// - 确保方法执行完成，不抛出异常
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task StopAllSoundsAsync()
    {
        try
        {
            _logger.LogDebug("停止所有音效播放");

            // 保持异步签名一致性，虽然这里没有真正的异步操作
            await Task.CompletedTask;

            // 停止所有音效播放器
            var players = new[]
            {
                (SystemSoundPlayer, "系统提示音"),
                (WarningSoundPlayer, "警告音效"),
                (SuccessSoundPlayer, "成功音效"),
                (ErrorSoundPlayer, "错误音效")
            };

            foreach (var (player, name) in players)
            {
                try
                {
                    player?.Stop();
                    _logger.LogDebug("已停止 {SoundName} 播放", name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "停止 {SoundName} 播放时发生异常", name);
                }
            }

            _logger.LogDebug("所有音效播放已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止所有音效播放时发生异常");
        }
    }

    /// <summary>
    /// 释放音效服务资源
    ///
    /// 功能：释放所有音效播放器和相关资源
    ///
    /// 使用场景：
    /// - 系统关闭时清理资源
    /// - 音效服务重新初始化前清理
    /// - 内存清理和垃圾回收
    ///
    /// 执行逻辑：
    /// - 停止所有正在播放的音效
    /// - 释放所有SoundPlayer实例
    /// - 清理预加载的音效数据
    /// - 重置所有播放器属性为null
    ///
    /// 异常处理：
    /// - 单个播放器释放失败不影响其他播放器
    /// - 记录详细的错误信息
    /// - 确保方法执行完成，不抛出异常
    ///
    /// 注意事项：
    /// - 调用此方法后需要重新初始化才能播放音效
    /// - 建议在应用程序关闭时调用
    /// - 实现IDisposable模式的一部分
    /// </summary>
    /// <returns>异步任务</returns>
    public async Task DisposeSoundServiceAsync()
    {
        try
        {
            _logger.LogInformation("开始释放音效服务资源");

            // 首先停止所有音效播放
            await StopAllSoundsAsync();

            // 释放所有音效播放器
            var players = new[]
            {
                (SystemSoundPlayer, "系统提示音"),
                (WarningSoundPlayer, "警告音效"),
                (SuccessSoundPlayer, "成功音效"),
                (ErrorSoundPlayer, "错误音效")
            };

            foreach (var (player, name) in players)
            {
                try
                {
                    player?.Dispose();
                    _logger.LogDebug("已释放 {SoundName} 播放器", name);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "释放 {SoundName} 播放器时发生异常", name);
                }
            }

            // 重置所有播放器属性
            SystemSoundPlayer = null;
            WarningSoundPlayer = null;
            SuccessSoundPlayer = null;
            ErrorSoundPlayer = null;

            _logger.LogInformation("音效服务资源释放完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放音效服务资源时发生异常");
        }
    }

    #endregion

    #region 私有方法 - 内部实现逻辑

    /// <summary>
    /// 确保音效目录存在
    ///
    /// 功能：检查并创建音效文件目录
    ///
    /// 执行逻辑：
    /// - 检查音效目录是否存在
    /// - 不存在时创建目录
    /// - 记录目录创建结果
    ///
    /// 异常处理：
    /// - 目录创建失败时记录错误日志
    /// - 不抛出异常，让调用方继续执行
    /// </summary>
    /// <returns>异步任务</returns>
    private async Task EnsureSoundsDirectoryExists()
    {
        try
        {
            // 保持异步签名一致性，虽然这里没有真正的异步操作
            await Task.CompletedTask;

            if (!Directory.Exists(SoundPaths.SoundsDirectory))
            {
                Directory.CreateDirectory(SoundPaths.SoundsDirectory);
                _logger.LogInformation("已创建音效目录: {Directory}", SoundPaths.SoundsDirectory);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建音效目录时发生异常: {Directory}", SoundPaths.SoundsDirectory);
        }
    }

    /// <summary>
    /// 加载音效播放器
    ///
    /// 功能：加载指定路径的音效文件并创建播放器
    ///
    /// 执行流程：
    /// 1. 检查音效文件是否存在
    /// 2. 创建SoundPlayer实例
    /// 3. 预加载音效文件
    /// 4. 验证加载结果
    ///
    /// 异常处理：
    /// - 文件不存在时返回null
    /// - 加载失败时记录错误并返回null
    /// - 不抛出异常，确保其他音效能正常加载
    /// </summary>
    /// <param name="filePath">音效文件路径</param>
    /// <param name="soundName">音效名称（用于日志）</param>
    /// <returns>音效播放器实例，失败时返回null</returns>
    private async Task<SoundPlayer?> LoadSoundPlayerAsync(string filePath, string soundName)
    {
        try
        {
            // 保持异步签名一致性，虽然这里没有真正的异步操作
            await Task.CompletedTask;

            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                _logger.LogWarning("音效文件不存在: {FilePath} ({SoundName})", filePath, soundName);
                return null;
            }

            // 创建并配置音效播放器
            var player = new SoundPlayer(filePath);

            // 预加载音效文件
            player.Load();

            _logger.LogDebug("成功加载音效: {SoundName} ({FilePath})", soundName, filePath);
            return player;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载音效失败: {SoundName} ({FilePath})", soundName, filePath);
            return null;
        }
    }

    /// <summary>
    /// 播放指定的音效
    ///
    /// 功能：播放指定的音效播放器
    ///
    /// 播放逻辑：
    /// - 检查播放器是否可用
    /// - 异步播放音效
    /// - 记录播放结果
    ///
    /// 异常处理：
    /// - 播放器为null时跳过播放
    /// - 播放失败时记录错误日志
    /// - 不抛出异常，确保主系统稳定运行
    /// </summary>
    /// <param name="player">音效播放器</param>
    /// <param name="soundName">音效名称（用于日志）</param>
    /// <returns>异步任务</returns>
    private async Task PlaySoundAsync(SoundPlayer? player, string soundName)
    {
        try
        {
            if (player == null)
            {
                _logger.LogDebug("音效播放器未初始化，跳过播放: {SoundName}", soundName);
                return;
            }

            // 保持异步签名一致性，虽然这里没有真正的异步操作
            await Task.CompletedTask;

            // 异步播放音效（不阻塞调用线程）
            player.Play();

            _logger.LogDebug("播放音效: {SoundName}", soundName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "播放音效时发生异常: {SoundName}", soundName);
        }
    }

    #endregion

    #region IDisposable 实现

    /// <summary>
    /// 释放资源
    ///
    /// 功能：实现IDisposable接口，确保资源正确释放
    ///
    /// 执行逻辑：
    /// - 检查是否已释放
    /// - 调用异步释放方法
    /// - 标记为已释放
    /// - 抑制终结器调用
    ///
    /// 设计模式：
    /// - 标准的IDisposable实现模式
    /// - 防止重复释放
    /// - 支持垃圾回收优化
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 同步调用异步释放方法
            // 注意：这里使用GetAwaiter().GetResult()是为了在Dispose中调用异步方法
            // 在实际应用中，建议优先使用异步的DisposeSoundServiceAsync方法
            DisposeSoundServiceAsync().GetAwaiter().GetResult();

            _disposed = true;
            GC.SuppressFinalize(this);
        }
    }

    #endregion
}
