using System.Diagnostics;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Forms;

public partial class FormMain : Form
{
    #region 私有字段

    /// <summary>
    /// 日志记录器
    /// 记录用户操作和系统事件，便于调试和监控
    /// </summary>
    private readonly ILogger<FormMain> _logger;

    /// <summary>
    /// 人员业务服务
    /// 提供人员数据的CRUD操作功能
    /// </summary>
    private readonly IMemberService _memberService;

    /// <summary>
    /// 期号时间服务
    /// 提供期号时间管理和监控功能
    /// </summary>
    private readonly IIssueTimeService _issueTimeService;

    /// <summary>
    /// 音效服务
    /// 提供音效播放和管理功能
    /// </summary>
    private readonly ISoundService _soundService;

    /// <summary>
    /// 取消令牌源，用于控制后台任务的取消
    /// </summary>
    private CancellationTokenSource? _cancellationTokenSource = new();

    #endregion

    #region 构造函数

    public FormMain(ILogger<FormMain> logger,
        IMemberService memberService,
        IIssueTimeService issueTimeService,
        ISoundService soundService)
    {
        // 初始化窗体组件（由设计器生成）
        InitializeComponent();

        // 依赖注入的服务实例
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _memberService = memberService ?? throw new ArgumentNullException(nameof(memberService));
        _issueTimeService = issueTimeService ?? throw new ArgumentNullException(nameof(issueTimeService));
        _soundService = soundService ?? throw new ArgumentNullException(nameof(soundService));
    }

    #endregion

    #region 窗体事件处理

    /// <summary>
    /// 窗体加载事件处理程序
    /// 加载数据并启动后台任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private async void FormMain_Load(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation("主窗体开始加载");
            List<Member> memberList = await _memberService.GetAllMemberAsync();
            dataGridView1.DataSource = memberList;

            // 创建发放时间数据
            _logger.LogDebug("正在创建发放时间数据...");
            await _issueTimeService.CreateIssueTimeAsync(DateTime.Now);
            _logger.LogDebug("发放时间数据创建完成");

            _logger.LogInformation("主窗体加载完成,开始启动服务");

            _ = Task.Run(() => _issueTimeService.UpdateCurrentIssueTimeAsync(_cancellationTokenSource!.Token));
            _ = Task.Run(() => ShowTimeInfo(_cancellationTokenSource!.Token));

            // 启动服务
            // await Task.WhenAll(
            //     _issueTimeService.UpdateCurrentIssueTimeAsync(_cancellationTokenSource!.Token),
            //     ShowTimeInfo(_cancellationTokenSource.Token)
            // );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $@"主窗体加载时发生异常,{ex}");
            MessageBox.Show($@"加载数据时发生错误: {ex.Message}", @"错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    /// <summary>
    /// 窗体关闭事件处理程序
    /// 清理资源并取消后台任务
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void FormMain_FormClosing(object sender, FormClosingEventArgs e)
    {
        // 清理自定义资源
        CleanupResources();
    }

    #endregion

    /// <summary>
    /// // 显示当前时间信息
    /// </summary>
    /// <param name="cancellationToken"></param>
    private async Task ShowTimeInfo(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(1000, cancellationToken);

                // 从IssueTimeService中获取当前期号信息
                var issueTime = _issueTimeService.GetCurrentCachedIssueTime();
                if (issueTime is null)
                {
                    continue;
                }

                // 计算时间差
                DateTime now = DateTime.Now;
                var openTimeSpan = (int)(issueTime.OpenTime - now).TotalSeconds;
                var closeTimeSpan = (int)(issueTime.CloseTime - now).TotalSeconds;

                // 根据状态显示不同内容
                if (openTimeSpan < 0 && closeTimeSpan > 0)
                {
                    Invoke(() =>
                    {
                        label_正在投注期数标题.Text = @"正在投注期数";
                        label_正在投注期数.Text = $@"{issueTime.Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(closeTimeSpan);
                    });
                }
                else
                {
                    Invoke(() =>
                    {
                        label_正在投注期数标题.Text = @"即将开盘";
                        label_正在投注期数.Text = $@"{issueTime.Issue}";
                        label_封盘倒计时.Text = FormatTimeRemaining(openTimeSpan);
                    });
                }


                // int openTimeSeconds = _issueTimeService.OpenTimeDownDic[CommonHelper.Lottery];
                // if (openTimeSeconds >= 0)
                // {
                //     if (openTimeSeconds > Setting.Current.封盘时间)
                //     {
                //         openTimeSeconds -= Setting.Current.封盘时间;
                //         Invoke(() => { label_正在投注期数标题.Text = @"正在投注期数"; });
                //     }
                //     else
                //     {
                //         Invoke(() => { label_正在投注期数标题.Text = @"即将开盘"; });
                //     }
                //
                //     int hour = openTimeSeconds / 3600;
                //     int minute = openTimeSeconds % 3600 / 60;
                //     int second = openTimeSeconds % 60;
                //
                //     Invoke(() =>
                //     {
                //         label_正在投注期数.Text = _issueTimeService.IssueTimeNowDic[CommonHelper.Lottery].Issue;
                //         label_封盘倒计时.Text = hour.ToString().PadLeft(2, '0') + @":" + minute.ToString().PadLeft(2, '0') + @":" + second.ToString().PadLeft(2, '0');
                //     });
                // }
                // else
                // {
                //     int closeTimeSeconds = _issueTimeService.CloseTimeDownDic[CommonHelper.Lottery];
                //     int hour = closeTimeSeconds / 3600;
                //     int minute = closeTimeSeconds % 3600 / 60;
                //     int second = closeTimeSeconds % 60;
                //
                //     Invoke(() =>
                //     {
                //         label_正在投注期数标题.Text = @"正在投注期数";
                //         label_正在投注期数.Text = _issueTimeService.IssueTimeNowDic[CommonHelper.Lottery].Issue;
                //         label_封盘倒计时.Text = hour.ToString().PadLeft(2, '0') + @":" + minute.ToString().PadLeft(2, '0') + @":" + second.ToString().PadLeft(2, '0');
                //     });
                // }
            }
            catch (Exception ex)
            {
                Debug.WriteLine(ex.ToString());
                await Task.Delay(0, cancellationToken);
            }
        }
    }

    /// <summary>
    /// 格式化时间显示
    /// </summary>
    /// <param name="totalSeconds">总秒数</param>
    /// <returns>格式化的时间字符串</returns>
    private string FormatTimeRemaining(int totalSeconds)
    {
        if (totalSeconds <= 0)
        {
            return "00:00:00";
        }

        // 大于等于1小时
        if (totalSeconds >= 3600)
        {
            var hours = (totalSeconds / 3600).ToString("D2");
            var minutes = ((totalSeconds % 3600) / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"{hours}:{minutes}:{seconds}";
        }

        // 大于等于1分钟
        if (totalSeconds >= 60)
        {
            var minutes = (totalSeconds / 60).ToString("D2");
            var seconds = (totalSeconds % 60).ToString("D2");
            return $"00:{minutes}:{seconds}";
        }

        // 小于1分钟
        return $"00:00:{totalSeconds.ToString("D2")}";
    }

    #region 资源清理

    /// <summary>
    /// 清理自定义资源
    /// 在窗体关闭时调用，用于清理取消令牌等资源
    /// </summary>
    private void CleanupResources()
    {
        try
        {
            _logger.LogDebug("开始清理应用程序资源");

            // 取消并释放取消令牌源
            _cancellationTokenSource?.Cancel();
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _logger.LogDebug("取消令牌已清理");

            // 清理Service层的缓存
            _issueTimeService.ClearCache();
            _logger.LogDebug("服务缓存已清理");

            _logger.LogDebug("应用程序资源清理完成");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "清理资源时发生错误");
        }
    }

    #endregion
}