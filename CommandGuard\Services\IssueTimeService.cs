﻿using CommandGuard.Configuration;
using CommandGuard.Interfaces;
using CommandGuard.Models;
using Microsoft.Extensions.Logging;

namespace CommandGuard.Services;

/// <summary>
/// 期号时间服务实现类
/// </summary>
public class IssueTimeService : IIssueTimeService
{
    #region 业务常量定义

    /// <summary>
    /// 每天期号发放次数
    ///
    /// 业务规则：每天固定发放203期
    /// 计算依据：从早上7点到次日6点55分，每5分钟一期
    /// 时间跨度：24小时 × 60分钟 ÷ 5分钟间隔 = 288期（理论值）
    /// 实际设置：203期（根据业务需求调整）
    ///
    /// 影响范围：
    /// - 数据库表记录数量
    /// - 内存缓存大小
    /// - 期号生成逻辑
    /// </summary>
    private const int DailyIssueCount = 203;

    /// <summary>
    /// 期号间隔时间（分钟）
    ///
    /// 业务规则：每期间隔5分钟
    /// 设计考虑：
    /// - 给用户足够的投注时间
    /// - 保持合理的开奖频率
    /// - 便于时间计算和显示
    ///
    /// 相关计算：
    /// - 下一期开放时间 = 当前期关闭时间 + 间隔时间
    /// - 每小时期数 = 60分钟 ÷ 5分钟 = 12期
    /// </summary>
    private const int IntervalMinutes = 5;

    /// <summary>
    /// 每期投注持续时间（分钟）
    ///
    /// 业务规则：每期投注时间为5分钟
    /// 时间分配：
    /// - 投注时间：5分钟
    /// - 间隔时间：0分钟（无间隔，连续开放）
    ///
    /// 实际投注时间：
    /// - 开放时间：期号开始时间
    /// - 关闭时间：开始时间 + 5分钟 - 10秒（提前关闭）
    /// - 有效投注时间：4分50秒
    /// </summary>
    private const int DurationMinutes = 5;

    /// <summary>
    /// 提前关闭投注时间（秒）
    ///
    /// 业务规则：在期号结束前10秒停止接受投注
    /// 设计目的：
    /// - 为开奖准备留出缓冲时间
    /// - 避免最后时刻的投注冲突
    /// - 确保开奖流程的稳定性
    ///
    /// 实际效果：
    /// - 名义投注时间：5分钟
    /// - 实际投注时间：4分50秒
    /// - 开奖准备时间：10秒
    /// </summary>
    private const int EarlyCloseSeconds = 10;

    /// <summary>
    /// 每天开始发放的小时
    ///
    /// 业务规则：每天早上7点开始第一期
    /// 选择依据：
    /// - 符合用户作息习惯
    /// - 避开深夜时段
    /// - 便于系统维护和管理
    ///
    /// 时间计算：
    /// - 第一期开放时间：每天07:00:00
    /// - 最后一期时间：根据期数和间隔自动计算
    /// - 跨日处理：自动处理跨越午夜的情况
    /// </summary>
    private const int StartHour = 7;

    /// <summary>
    /// 民国元年（公元1912年）
    ///
    /// 历史背景：中华民国成立于1912年，民国纪年从此开始
    /// 计算公式：民国年份 = 公元年份 - 1911
    ///
    /// 期号格式：民国年份 + 6位序号
    /// 示例：
    /// - 2024年 → 民国113年 → 期号前缀：113
    /// - 第1期 → 113000001
    /// - 第203期 → 113000203
    ///
    /// 设计优势：
    /// - 期号长度固定，便于排序和查询
    /// - 包含年份信息，便于数据管理
    /// - 符合传统习惯，用户易于理解
    /// </summary>
    private const int RepublicOfChinaFirstYear = 1912;

    #endregion

    #region 私有字段和依赖

    /// <summary>
    /// 日志记录器
    ///
    /// 功能：记录服务运行过程中的详细信息
    /// 日志级别：
    /// - Debug：缓存命中、状态检查等调试信息
    /// - Information：状态变化、数据创建等重要操作
    /// - Warning：异常恢复、数据不一致等警告
    /// - Error：数据库错误、计算异常等错误信息
    ///
    /// 日志内容：
    /// - 期号数据的创建和更新
    /// - 状态变化的详细记录
    /// - 性能指标和缓存命中率
    /// - 异常情况和恢复过程
    /// </summary>
    private readonly ILogger<IssueTimeService> _logger;

    /// <summary>
    /// 数据库服务接口
    ///
    /// 功能：提供数据库访问能力和连接管理
    /// 核心能力：
    /// - FreeSql ORM实例访问
    /// - 数据库连接池管理
    /// - 事务处理支持
    /// - 线程安全的数据库操作锁
    ///
    /// 使用场景：
    /// - 期号数据的批量创建和查询
    /// - 数据库结构的自动同步
    /// - 复杂查询和数据统计
    /// - 数据库连接异常的处理
    /// </summary>
    private readonly IDbService _dbService;

    /// <summary>
    /// 缓存当前期号信息
    /// </summary>
    private IssueTime? _cachedCurrentIssueTime;

    /// <summary>
    /// 读写锁对象
    /// </summary>
    private readonly ReaderWriterLockSlim _cacheRwLock = new();

    #endregion

    #region 构造函数和初始化

    /// <summary>
    /// 期号时间服务构造函数
    ///
    /// 功能：通过依赖注入初始化服务所需的核心依赖
    ///
    /// 依赖注入：
    /// - ILogger：用于记录服务运行日志
    /// - IDbService：用于数据库访问和操作
    ///
    /// 初始化过程：
    /// 1. 验证依赖参数的有效性
    /// 2. 初始化内部字段和状态
    /// 3. 准备缓存和锁对象
    /// 4. 设置初始状态为Unknown
    ///
    /// 异常处理：
    /// - 参数为null时抛出ArgumentNullException
    /// - 确保服务创建失败时能够快速发现问题
    ///
    /// 生命周期：
    /// - 通常注册为单例服务
    /// - 在应用程序启动时创建
    /// - 在应用程序关闭时释放
    ///
    /// 线程安全：
    /// - 构造函数本身是线程安全的
    /// - 创建后的实例支持多线程访问
    /// </summary>
    /// <param name="logger">日志记录器，用于记录服务运行状态和异常信息</param>
    /// <param name="dbService">数据库服务，提供数据访问和连接管理功能</param>
    /// <exception cref="ArgumentNullException">当任何参数为null时抛出</exception>
    public IssueTimeService(ILogger<IssueTimeService> logger, IDbService dbService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));

        _logger.LogDebug("期号时间服务实例已创建，初始状态为Unknown");
    }

    #endregion

    #region 公共缓存访问方法

    /// <summary>
    /// 线程安全地获取当前缓存的发放时间信息
    ///
    /// 功能：为多线程环境提供安全的缓存读取接口
    ///
    /// 性能优化：
    /// - 使用读写锁，允许多个线程并发读取
    /// - 读取操作不会阻塞其他读取操作
    /// - 只有在写入时才会阻塞读取
    ///
    /// 线程安全：
    /// - 使用ReaderWriterLockSlim确保读取安全
    /// - 防止在读取过程中缓存被修改
    /// - 返回的是引用，调用方不应修改返回的对象
    ///
    /// 使用场景：
    /// - 多个业务模块需要获取当前期号信息
    /// - 高频率的期号状态查询
    /// - 实时显示当前期号和时间信息
    ///
    /// 注意事项：
    /// - 返回null表示缓存未初始化或已清除
    /// - 返回的对象不应被修改，以保持缓存一致性
    /// - 调用方应该检查返回值是否为null
    /// </summary>
    /// <returns>当前缓存的发放时间对象，如果缓存为空则返回null</returns>
    public IssueTime? GetCurrentCachedIssueTime()
    {
        _cacheRwLock.EnterReadLock();
        try
        {
            return _cachedCurrentIssueTime;
        }
        finally
        {
            _cacheRwLock.ExitReadLock();
        }
    }

    #endregion

    #region 数据创建和管理方法

    /// <summary>
    /// 创建发放记录
    /// 生成一整年的发放时间数据，每天203次，每次间隔5分钟
    /// </summary>
    public async Task<IssueTime> CreateIssueTimeAsync(DateTime dateTime)
    {
        var targetYear = dateTime.Year;
        _logger.LogDebug($"开始创建 {targetYear} 年的发放时间数据");

        try
        {
            // 检查指定年份是否已有数据
            var existingFirstIssueTime = await _dbService.FreeSql.Select<IssueTime>()
                .Where(x => x.OpenTime.Year == targetYear)
                .OrderBy(x => x.OpenTime)
                .FirstAsync();

            if (existingFirstIssueTime != null)
            {
                _logger.LogDebug($"{targetYear} 年的发放时间数据已存在，返回第一条记录: {existingFirstIssueTime.Issue}");
                return existingFirstIssueTime;
            }

            // 生成全年发放时间数据
            var issueTimeList = GenerateYearlyIssueTimeData(targetYear);

            _logger.LogInformation($"生成了 {issueTimeList.Count} 条 {targetYear} 年的发放时间记录，开始批量插入数据库");

            // 批量插入数据库
            var affectedRows = await _dbService.FreeSql.Insert(issueTimeList).ExecuteAffrowsAsync();

            _logger.LogInformation($"成功插入 {affectedRows} 条 {targetYear} 年的发放时间记录");

            return issueTimeList[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"创建 {targetYear} 年发放时间数据时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 生成指定年份的全年发放时间数据
    /// </summary>
    /// <param name="year">目标年份</param>
    /// <returns>发放时间数据列表</returns>
    private List<IssueTime> GenerateYearlyIssueTimeData(int year)
    {
        var issueTimeList = new List<IssueTime>();
        var index = 0; // 全局序号计数器
        var mingGuoYear = year - RepublicOfChinaFirstYear + 1; // 民国年份

        _logger.LogDebug($"开始生成 {year} 年数据，民国年份: {mingGuoYear}");

        for (int month = 1; month <= 12; month++)
        {
            var daysInMonth = DateTime.DaysInMonth(year, month);

            for (int day = 1; day <= daysInMonth; day++)
            {
                // 每天从开始时间前5分钟开始
                var currentTime = new DateTime(year, month, day, StartHour, 0, 0).AddMinutes(-IntervalMinutes);

                for (int i = 0; i < DailyIssueCount; i++)
                {
                    var openTime = currentTime.AddMinutes(IntervalMinutes);
                    var closeTime = openTime.AddMinutes(DurationMinutes).AddSeconds(-EarlyCloseSeconds).AddSeconds(-UserSetting.Current.封盘时间);
                    index++;

                    var issueNumber = GenerateIssueNumber(mingGuoYear, index);

                    issueTimeList.Add(new IssueTime
                    {
                        Issue = issueNumber,
                        OpenTime = openTime,
                        CloseTime = closeTime
                    });

                    currentTime = openTime; // 更新到下一个时间点
                }
            }
        }

        var daysInYear = DateTime.IsLeapYear(year) ? 366 : 365;
        _logger.LogDebug($"{year} 年共 {daysInYear} 天，生成 {index} 条发放记录");

        return issueTimeList;
    }

    /// <summary>
    /// 生成发放编号
    /// </summary>
    /// <param name="mingGuoYear">民国年份</param>
    /// <param name="sequenceNumber">序号</param>
    /// <returns>发放编号</returns>
    private static string GenerateIssueNumber(int mingGuoYear, int sequenceNumber)
    {
        return $"{mingGuoYear}{sequenceNumber:D6}";
    }

    #endregion

    #region 数据维护方法

    /// <summary>
    /// 获取当前正在进行的发放时间段
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                await Task.Delay(1000, cancellationToken);
                DateTime now = DateTime.Now;

                // 检查缓存是否有效（使用读写锁优化性能）
                _cacheRwLock.EnterReadLock();
                try
                {
                    if (_cachedCurrentIssueTime != null && IsCacheValid(_cachedCurrentIssueTime, now))
                    {
                        _logger.LogDebug($"使用缓存数据: {_cachedCurrentIssueTime.Issue}");
                        continue;
                    }
                }
                finally
                {
                    _cacheRwLock.ExitReadLock();
                }

                // 缓存无效，需要重新查询数据库
                _logger.LogDebug($"缓存过期，重新查询数据库 - 当前时间: {now:yyyy-MM-dd HH:mm:ss}");
                IssueTime? resultIssueTime = null;

                // 优化：使用单个查询同时查找当前和下一个时间段
                var candidateIssues = await _dbService.FreeSql.Select<IssueTime>()
                    .Where(x => x.CloseTime >= now || x.OpenTime > now) // 包含当前正在进行的和未来的时间段
                    .OrderBy(x => x.OpenTime)
                    .Take(2) // 最多取2条：当前进行中的和下一个
                    .ToListAsync(cancellationToken);

                if (candidateIssues.Count > 0)
                {
                    // 优先选择当前正在进行的时间段
                    var currentIssue = candidateIssues.FirstOrDefault(x => x.OpenTime <= now && x.CloseTime >= now);
                    if (currentIssue != null)
                    {
                        resultIssueTime = currentIssue;
                        _logger.LogInformation($"找到当前进行中的发放时间段: {currentIssue.Issue} ({currentIssue.OpenTime:HH:mm:ss} - {currentIssue.CloseTime:HH:mm:ss})");
                    }
                    else
                    {
                        // 没有当前进行的，选择最近的未来时间段
                        var nextIssue = candidateIssues.FirstOrDefault(x => x.OpenTime > now);
                        if (nextIssue != null)
                        {
                            resultIssueTime = nextIssue;
                            _logger.LogInformation($"找到下一个发放时间段: {nextIssue.Issue} ({nextIssue.OpenTime:HH:mm:ss} - {nextIssue.CloseTime:HH:mm:ss})");
                        }
                    }
                }

                // 如果没有找到任何时间段，则创建下一年的数据
                resultIssueTime ??= await HandleNoFutureIssueTimeAsync(now);

                // 更新缓存
                if (resultIssueTime != null)
                {
                    UpdateCache(resultIssueTime);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "更新发放时间时发生错误");
        }
    }

    /// <summary>
    /// 处理没有找到未来时间段的情况，自动创建下一年数据
    /// </summary>
    /// <param name="currentTime">当前时间</param>
    /// <returns>新创建的第一个发放时间段</returns>
    private async Task<IssueTime?> HandleNoFutureIssueTimeAsync(DateTime currentTime)
    {
        try
        {
            // 确定下一年的年份
            var nextYear = currentTime.Year + 1;
            var nextYearDate = new DateTime(nextYear, 1, 1);

            _logger.LogInformation($"当前年份 {currentTime.Year} 没有未来的发放时间段，开始创建 {nextYear} 年的数据");

            var newFirstIssueTime = await CreateIssueTimeAsync(nextYearDate);

            _logger.LogInformation($"成功创建 {nextYear} 年的发放时间数据，第一个时间段: {newFirstIssueTime.Issue}");

            return newFirstIssueTime;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "创建下一年发放时间数据时发生错误");
            return null;
        }
    }

    #endregion

    #region 辅助方法

    /// <summary>
    /// 更新缓存（线程安全）
    /// 使用写锁确保更新操作的原子性和一致性
    /// 同时更新期号信息和状态，确保数据一致性
    /// </summary>
    /// <param name="issueTime">要缓存的发放时间对象</param>
    private void UpdateCache(IssueTime issueTime)
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            _cachedCurrentIssueTime = issueTime;

            _logger.LogInformation($"缓存已更新: {issueTime.Issue} ({issueTime.OpenTime:HH:mm:ss} - {issueTime.CloseTime:HH:mm:ss})");
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// 使用写锁确保清除操作的原子性
    /// 同时重置状态为Unknown
    /// </summary>
    public void ClearCache()
    {
        _cacheRwLock.EnterWriteLock();
        try
        {
            var hadCache = _cachedCurrentIssueTime != null;

            _cachedCurrentIssueTime = null;

            if (hadCache)
            {
                _logger.LogInformation("发放时间缓存已清除");
            }
        }
        finally
        {
            _cacheRwLock.ExitWriteLock();
        }
    }

    /// <summary>
    /// 检查缓存是否仍然有效
    /// 优化后的缓存策略：最大化缓存利用率，最小化数据库查询
    /// </summary>
    /// <param name="cachedIssueTime">缓存的发放时间对象</param>
    /// <param name="currentTime">当前时间</param>
    /// <returns>如果缓存有效返回true，否则返回false</returns>
    private bool IsCacheValid(IssueTime cachedIssueTime, DateTime currentTime)
    {
        // 情况1：当前时间在发放时间段内 - 缓存绝对有效
        if (currentTime <= cachedIssueTime.CloseTime)
        {
            _logger.LogTrace($"缓存有效：当前时间在发放时间段内 ({cachedIssueTime.OpenTime:HH:mm:ss} - {cachedIssueTime.CloseTime:HH:mm:ss})");
            return true;
        }

        return false;
    }

    #endregion

    #region 资源管理和释放

    /// <summary>
    /// 释放服务占用的系统资源
    ///
    /// 功能：确保服务关闭时正确释放所有占用的系统资源
    ///
    /// 释放资源：
    /// - ReaderWriterLockSlim：读写锁对象
    /// - 清理缓存引用：避免内存泄漏
    /// - 重置状态：确保服务状态清洁
    ///
    /// 调用时机：
    /// - 应用程序关闭时
    /// - 服务容器释放时
    /// - 手动释放资源时
    ///
    /// 最佳实践：
    /// - 实现IDisposable接口
    /// - 支持多次调用Dispose
    /// - 释放后标记对象状态
    ///
    /// 线程安全：
    /// - Dispose方法本身是线程安全的
    /// - 释放过程中会阻塞其他操作
    /// - 释放后的对象不应再被使用
    ///
    /// 注意事项：
    /// - 释放后不应再调用任何业务方法
    /// - 建议在应用程序关闭时自动调用
    /// - 可以通过依赖注入容器自动管理
    /// </summary>
    public void Dispose()
    {
        try
        {
            // 释放读写锁资源
            _cacheRwLock.Dispose();

            // 清理缓存引用
            _cachedCurrentIssueTime = null;

            _logger.LogInformation("期号时间服务资源已释放");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "释放期号时间服务资源时发生异常");
        }
    }

    #endregion
}