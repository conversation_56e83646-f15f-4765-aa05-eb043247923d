﻿using CommandGuard.Models;

namespace CommandGuard.Interfaces;

/// <summary>
/// 期号时间服务接口
///
/// 功能概述：
/// - 提供期号时间数据的创建、查询和管理功能
/// - 支持高性能的多线程安全缓存访问
/// - 实时维护期号状态信息
/// - 为投注业务提供状态判断支持
///
/// 核心特性：
/// - 自动生成全年期号数据（每天203期，每期5分钟）
/// - 智能缓存机制，减少数据库查询
/// - 实时状态计算和更新
/// - 线程安全的并发访问支持
/// - 状态变化自动日志记录
///
/// 业务价值：
/// - 为投注系统提供准确的时间控制
/// - 支持实时的投注开放/关闭判断
/// - 优化系统性能，提升用户体验
/// - 便于业务监控和问题排查
///
/// 资源管理：
/// - 实现IDisposable接口，支持资源自动释放
/// - 通过依赖注入容器管理生命周期
/// - 确保系统关闭时正确清理资源
/// </summary>
public interface IIssueTimeService : IDisposable
{
    /// <summary>
    /// 创建期号时间数据
    ///
    /// 功能：为指定年份生成完整的期号时间数据
    ///
    /// 生成规则：
    /// - 每天203期，从早上7点开始
    /// - 每期间隔5分钟，持续5分钟
    /// - 期号格式：民国年份+6位序号（如：113000001）
    /// - 自动处理闰年和平年的天数差异
    ///
    /// 使用场景：
    /// - 系统初始化时创建当年数据
    /// - 跨年时自动创建下一年数据
    /// - 数据重建或修复时使用
    ///
    /// 注意事项：
    /// - 如果指定年份数据已存在，直接返回第一条记录
    /// - 批量插入操作，性能优化
    /// - 支持异步操作，不阻塞主线程
    /// </summary>
    /// <param name="issueTimeYear">目标年份的任意日期</param>
    /// <returns>创建的第一条期号时间记录</returns>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 维护更新当前期号时间信息
    ///
    /// 功能：后台持续运行的服务，负责维护当前期号缓存和状态
    ///
    /// 工作机制：
    /// - 每秒检查一次当前时间和期号状态
    /// - 智能缓存策略，减少数据库查询频率
    /// - 实时计算和更新期号状态
    /// - 自动处理期号切换和跨年数据创建
    ///
    /// 状态管理：
    /// - 根据当前时间自动计算期号状态
    /// - 状态变化时记录详细日志
    /// - 支持状态变化事件通知
    ///
    /// 异常处理：
    /// - 数据库连接异常时的重试机制
    /// - 状态计算异常时的恢复策略
    /// - 详细的错误日志记录
    ///
    /// 性能优化：
    /// - 缓存有效期智能判断
    /// - 读写锁优化并发性能
    /// - 最小化数据库访问频率
    /// </summary>
    /// <param name="cancellationToken">取消令牌，用于优雅停止服务</param>
    /// <returns>持续运行的异步任务</returns>
    Task UpdateCurrentIssueTimeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 线程安全地获取当前缓存的期号时间信息
    ///
    /// 功能：高性能的期号信息读取接口
    ///
    /// 性能特点：
    /// - 使用读锁，支持多线程并发访问
    /// - 直接返回缓存对象引用，性能最优
    /// - 适合高频率的状态查询场景
    ///
    /// 使用场景：
    /// - 投注前的期号信息获取
    /// - UI界面的实时信息显示
    /// - 业务逻辑中的时间判断
    /// - 系统监控和状态检查
    ///
    /// 注意事项：
    /// - 返回的是对象引用，调用方不应修改返回的对象
    /// - 如果需要修改数据，请使用 GetCurrentCachedIssueTimeCopy()
    /// - 返回null表示缓存未初始化或系统正在启动
    ///
    /// 线程安全：
    /// - 读取操作完全线程安全
    /// - 不会阻塞其他读取操作
    /// - 只在写入时短暂阻塞
    /// </summary>
    /// <returns>当前缓存的期号时间对象，缓存为空时返回null</returns>
    IssueTime? GetCurrentCachedIssueTime();

    /// <summary>
    /// 清除期号时间缓存
    ///
    /// 功能：清空当前缓存的期号信息和状态，强制下次查询时重新从数据库获取
    ///
    /// 执行操作：
    /// - 清空缓存的期号时间对象
    /// - 重置状态为Unknown
    /// - 记录缓存清除日志
    /// - 下次访问时触发数据库查询
    ///
    /// 使用场景：
    /// - 数据库数据更新后的缓存刷新
    /// - 系统异常后的状态重置
    /// - 手动数据刷新操作
    /// - 调试和测试时的状态清理
    ///
    /// 影响范围：
    /// - 清除后所有状态查询将返回Unknown
    /// - 下次UpdateCurrentIssueTimeAsync循环将重新加载数据
    /// - 可能导致短暂的服务不可用
    ///
    /// 注意事项：
    /// - 此操作会获取写锁，短暂阻塞其他操作
    /// - 清除后需要等待后台服务重新加载数据
    /// - 建议在系统维护时使用
    /// </summary>
    void ClearCache();
}